---

## **MySQL Schema Reader MCP工具设计报告**

**版本:** 1.0
**日期:** 2025年7月15日

### **1. 引言 (Introduction)**

#### **1.1. 项目目标与范围**
**项目目标**: 本项目旨在开发一款名为“MySQL Schema Reader”的命令行（CLI）工具。其主要目的是为开发人员、数据库管理员（DBA）和AI模型提供一个快速、便捷的接口，以便在不直接访问图形化数据库管理工具的情况下，查询和理解MySQL数据库中的表结构。该工具旨在简化开发和分析流程，尤其是在远程服务器操作或自动化脚本中，能够通过简单的命令获取清晰的表结构信息。

**项目范围**:
*   **功能**: 工具的核心功能是读取并显示数据库表的结构。
*   **不支持**: 工具范围不包括数据的增、删、改（DML）或数据库对象的修改（DDL），也不支持复杂的数据库管理任务。它是一个只读工具。
*   **交互**: 工具主要通过命令行界面（CLI）和作为模型上下文协议（MCP）工具被AI模型调用。

#### **1.2. 名词解释**
*   **MCP (Model Context Protocol)**: 模型上下文协议，一个使大型语言模型能够安全、结构化地与外部工具和服务进行交互的协议。
*   **CLI (Command-Line Interface)**: 命令行界面，一种允许用户通过输入文本命令与计算机程序进行交互的用户界面。
*   **数据库表结构 (Database Table Schema)**: 对数据库中表的布局的描述，包括列名、数据类型、约束（如主键、外键）、是否允许为空（NULL）、默认值等元数据。

#### **1.3. 目标用户**
*   **后端开发人员**: 在开发过程中需要快速查看表结构以编写代码。
*   **数据库管理员 (DBA)**: 进行快速检查或在脚本中自动化获取表信息。
*   **数据分析师**: 在进行数据探索前，了解数据表的字段和类型。
*   **AI模型**: 作为MCP工具，被AI模型调用以响应用户关于数据库结构的问题。

### **2. 系统概述 (System Overview)**

#### **2.1. 系统架构**
该工具的架构简洁明了，主要由四部分组成：

1.  **用户/AI模型**: 交互的发起者，通过CLI或MCP调用发出指令。
2.  **CLI接口**: 接收并解析用户输入的命令和参数。
3.  **MCP工具核心逻辑**:
    *   读取并解析配置文件以获取数据库凭证。
    *   管理与MySQL数据库的连接。
    *   根据解析的命令，生成并执行相应的SQL查询。
    *   解析数据库返回的结果。
    *   将结果格式化为人类可读的文本（用于CLI）或机器可读的JSON（用于MCP）。
4.  **MySQL数据库**: 目标数据库，存储着需要查询的表结构信息。

**交互流程示意**:
`用户/AI模型 -> CLI -> 核心逻辑 (读取配置, 连接数据库, 执行查询) -> MySQL数据库`
`MySQL数据库 -> 核心逻辑 (解析数据, 格式化输出) -> CLI -> 用户/AI模型`

#### **2.2. 核心功能**
*   通过外部配置文件（如 `config.ini`）安全地管理和连接到MySQL数据库。
*   通过命令行界面接收用户的指令，支持不同的命令和参数。
*   解析用户输入的目标表名或其它指令。
*   查询并返回指定表的详细结构信息，包括字段名、类型、键信息等。
*   以清晰、易于阅读的ASCII表格格式在命令行中展示结果。
*   提供结构化的JSON输出，以便与AI模型等自动化系统集成。

### **3. 功能设计 (Functional Design)**

#### **3.1. 数据库连接模块**

##### **3.1.1. 配置文件 (`config.ini`)**
为了安全和灵活性，数据库凭证将存储在INI格式的配置文件中。默认情况下，工具将在当前工作目录查找 `config.ini` 文件。

**文件结构**:
```ini
[mysql]
host = 127.0.0.1
port = 3306
user = root
password = your_password_here
database = default_database_name
```

*   `host`: 数据库服务器的主机名或IP地址。
*   `port`: 数据库服务器的端口号。
*   `user`: 连接数据库的用户名。
*   `password`: 对应的密码。
*   `database`: 默认连接的数据库名称，可在命令行参数中覆盖。

##### **3.1.2. 连接逻辑**
工具启动时，会首先加载并解析配置文件。它将使用这些参数尝试与MySQL数据库建立连接。初步设计将采用按需连接的方式，即每次执行命令时建立一个新连接，并在命令执行完毕后关闭。
**未来考虑**: 如果工具的使用频率非常高，可以引入一个简单的连接池设计，以减少连接和断开的开销，但这不包含在版本1.0的范围内。

#### **3.2. 表结构读取模块**

##### **3.2.1. 查询命令**
为了获取表结构，将优先使用 `DESCRIBE` SQL命令，因为它简洁高效，能提供所需的所有关键信息。

**示例查询**:
```sql
DESCRIBE `table_name`;
```
*注意：表名将使用反引号（`）包裹，以避免与SQL保留关键字冲突。*

##### **3.2.2. 数据解析**
执行 `DESCRIBE` 命令后，数据库将返回一个结果集，每一行代表一个表字段。核心逻辑将遍历此结果集，并提取以下信息：
*   `Field`: 字段名
*   `Type`: 数据类型（如 `varchar(255)`, `int(11)`）
*   `Null`: 是否允许为NULL（`YES` 或 `NO`）
*   `Key`: 键信息（如 `PRI` 表示主键, `MUL` 表示索引, `UNI` 表示唯一键）
*   `Default`: 默认值
*   `Extra`: 额外信息（如 `auto_increment`）

#### **3.3. 输出格式化模块**

##### **3.3.1. CLI输出**
为了在命令行中提供最佳的可读性，输出将被格式化为ASCII表格。

**输出格式示例**:
```
+-----------------+--------------+------+-----+---------+----------------+
| Field           | Type         | Null | Key | Default | Extra          |
+-----------------+--------------+------+-----+---------+----------------+
| id              | int(11)      | NO   | PRI | NULL    | auto_increment |
| username        | varchar(50)  | NO   | UNI | NULL    |                |
| email           | varchar(100) | NO   |     | NULL    |                |
| created_at      | timestamp    | YES  |     | NULL    |                |
+-----------------+--------------+------+-----+---------+----------------+
```

### **4. 用户交互设计 (User Interaction Design)**

#### **4.1. 命令行接口 (CLI)**

##### **4.1.1. 命令语法**
*   **查询指定表的结构**:
    ```bash
    mcp-mysql-reader describe --table <table_name>
    ```
*   **列出当前数据库中的所有表**:
    ```bash
    mcp-mysql-reader show-tables
    ```

##### **4.1.2. 命令行参数**
*   `--table <table_name>`: (必需，用于`describe`命令) 指定要查询的表名。
*   `--database <database_name>`: (可选) 指定要连接的数据库。如果提供，将覆盖配置文件中的默认数据库。
*   `--config <path_to_config>`: (可选) 指定配置文件的路径。如果未提供，则在当前目录查找 `config.ini`。
*   `--help`: (可选) 显示帮助信息和可用命令。

#### **4.2. 交互流程**
一个典型的用户使用场景如下：
1.  用户在终端中输入命令：`mcp-mysql-reader describe --table users`。
2.  工具的CLI模块解析命令为 `describe`，并识别出参数 `table` 的值为 `users`。
3.  工具读取 `config.ini` 文件，获取数据库连接信息。
4.  工具使用这些信息建立到MySQL数据库的连接。
5.  工具构建并执行SQL查询：`DESCRIBE \`users\`;`。
6.  工具接收数据库返回的表结构数据。
7.  输出格式化模块将数据渲染成ASCII表格。
8.  最终的表格显示在用户的终端上。
9.  工具关闭数据库连接并退出。

### **5. MCP工具集成 (MCP Tool Integration)**

#### **5.1. 工具定义 (Tool Definition)**
为了让AI模型能够调用此工具，需要提供一个结构化的工具定义。

*   **name**: `mysql_schema_reader`
*   **description**: "一个可以连接到MySQL数据库并返回指定表结构的工具。可用于查询表的字段名、数据类型、键信息等。"
*   **inputSchema**:
    ```json
    {
      "type": "object",
      "properties": {
        "tableName": {
          "type": "string",
          "description": "需要查询其结构的表的名称。"
        }
      },
      "required": ["tableName"]
    }
    ```
*   **outputSchema**:
    ```json
    {
      "type": "object",
      "properties": {
        "schema": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "columnName": { "type": "string", "description": "列（字段）的名称。" },
              "dataType": { "type": "string", "description": "列的数据类型。" },
              "isNullable": { "type": "boolean", "description": "该列是否允许存储NULL值。" },
              "keyInfo": { "type": "string", "description": "键信息 (例如 'PRI' 表示主键)。" },
              "defaultValue": { "type": ["string", "null"], "description": "该列的默认值。" },
              "extraInfo": { "type": "string", "description": "额外信息 (例如 'auto_increment')。" }
            },
            "required": ["columnName", "dataType", "isNullable", "keyInfo", "defaultValue", "extraInfo"]
          }
        }
      }
    }
    ```

#### **5.2. 调用流程**
1.  用户向AI模型提问：“请告诉我用户表的结构是怎样的？”
2.  AI模型识别出用户的意图是查询数据库表结构。
3.  模型在其可用工具列表中，根据描述找到了 `mysql_schema_reader` 工具。
4.  模型从用户的问题中提取出关键词“用户表”，并将其映射到 `inputSchema` 中的 `tableName` 参数，值为 "users"。
5.  模型构造一个对工具的调用请求：`mysql_schema_reader(tableName='users')`。
6.  工具执行后，返回符合 `outputSchema` 格式的JSON数据。
7.  AI模型接收此JSON数据，并将其转换为自然语言，回复给用户：“用户表包含id、username、email等字段。其中id是主键，类型是整数，并且会自动递增...”。

### **6. 错误处理 (Error Handling)**
工具将提供清晰、有帮助的错误信息来处理异常情况。

*   **配置文件缺失**: `错误：配置文件 'config.ini' 未找到。请在当前目录创建该文件或使用 --config 参数指定路径。`
*   **配置文件格式错误**: `错误：配置文件 'config.ini' 解析失败。请检查文件格式是否正确。`
*   **数据库连接失败**: `错误：无法连接到数据库。请检查配置文件中的主机、端口、用户名和密码是否正确，并确保数据库服务正在运行。`
*   **指定的表不存在**: `错误：在数据库 '<database_name>' 中未找到表 '<table_name>'。`
*   **用户输入的命令或参数无效**: `错误：无效的命令 '<command>'。请使用 'mcp-mysql-reader --help' 查看可用命令。`
*   **没有权限访问**: `错误：用户 '<user>' 没有足够的权限访问数据库 '<database_name>' 或表 '<table_name>'。`

### **7. 安全性考虑 (Security Considerations)**

#### **7.1. 凭证管理**
*   **禁止硬编码**: 数据库密码等敏感信息严禁硬编码在源代码中。
*   **配置文件**: 所有凭证必须通过外部配置文件（`config.ini`）管理。
*   **文件权限**: 强烈建议用户为配置文件设置严格的访问权限（例如，在Linux/macOS上使用 `chmod 600 config.ini`），以防止其他用户读取敏感信息。

#### **7.2. SQL注入**
尽管此工具的功能不涉及处理用户提供的“数据”，但表名本身也是输入。为了防止潜在的SQL注入风险（虽然在 `DESCRIBE` 等DDL/DQL语句中风险较低），将采取以下措施：
*   **输入验证**: 对用户提供的表名进行严格的验证，确保它只包含合法的字符（例如，字母、数字、下划线），不含分号、注释符等危险字符。
*   **不拼接复杂查询**: 工具的功能被严格限制，不会将用户输入拼接到复杂的SQL查询字符串中。

### **8. 部署与维护 (Deployment and Maintenance)**

#### **8.1. 依赖**
该工具将基于Python开发，主要依赖以下库：
*   `mysql-connector-python`: 用于连接MySQL数据库。
*   `configparser`: (Python标准库) 用于解析 `.ini` 配置文件。
*   `argparse`: (Python标准库) 用于构建强大的命令行接口。
*   `prettytable` (可选，推荐): 用于方便地生成格式化的ASCII表格。

#### **8.2. 安装说明**
1.  **安装依赖**:
    ```bash
    pip install mysql-connector-python prettytable
    ```
2.  **创建配置文件**: 在您的工作目录中创建一个名为 `config.ini` 的文件，并填入您的数据库连接信息。
    ```ini
    [mysql]
    host = 127.0.0.1
    port = 3306
    user = your_user
    password = your_password
    database = your_db
    ```
3.  **运行工具**: 将工具脚本（例如 `mcp-mysql-reader.py`）放置在可执行路径下，然后运行：
    ```bash
    python mcp-mysql-reader.py describe --table my_table
    ```

---