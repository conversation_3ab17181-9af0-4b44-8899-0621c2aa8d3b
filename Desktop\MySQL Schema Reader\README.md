# MySQL Schema Reader MCP 工具

一个用于读取MySQL数据库表结构的命令行工具。支持CLI输出（ASCII表格）和用于MCP集成的JSON输出。

## 功能特性

- 查询MySQL表结构及详细列信息
- 列出数据库中的所有表
- ASCII表格输出，便于人类阅读
- JSON输出，用于MCP（模型上下文协议）集成
- 基于配置文件的安全凭证管理
- 全面的错误处理，提供中文错误信息
- SQL注入防护

## 安装说明

1. **安装Python依赖：**
   ```bash
   pip install mysql-connector-python prettytable
   ```

2. **创建配置文件：**
   将 `config.ini.example` 复制为 `config.ini` 并更新为您的数据库凭证：
   ```ini
   [mysql]
   host = 127.0.0.1
   port = 3306
   user = your_username
   password = your_password
   database = your_database_name
   ```

3. **设置安全文件权限（推荐）：**
   ```bash
   # 在 Linux/macOS 上
   chmod 600 config.ini
   ```

## 使用方法

### 基本命令

**描述表结构：**
```bash
python mcp_mysql_reader.py describe --table users
```

**列出数据库中的所有表：**
```bash
python mcp_mysql_reader.py show-tables
```

### 高级选项

**使用自定义配置文件：**
```bash
python mcp_mysql_reader.py describe --table products --config /path/to/config.ini
```

**覆盖配置中的数据库：**
```bash
python mcp_mysql_reader.py describe --table orders --database shop_db
```

**以JSON格式输出（用于MCP集成）：**
```bash
python mcp_mysql_reader.py describe --table users --json
```

**显示帮助信息：**
```bash
python mcp_mysql_reader.py --help
```

## 输出示例

### ASCII表格输出（CLI）
```
+-----------------+--------------+------+-----+---------+----------------+
| Field           | Type         | Null | Key | Default | Extra          |
+-----------------+--------------+------+-----+---------+----------------+
| id              | int(11)      | NO   | PRI | NULL    | auto_increment |
| username        | varchar(50)  | NO   | UNI | NULL    |                |
| email           | varchar(100) | NO   |     | NULL    |                |
| created_at      | timestamp    | YES  |     | NULL    |                |
+-----------------+--------------+------+-----+---------+----------------+
```

### JSON输出（MCP）
```json
{
  "schema": [
    {
      "columnName": "id",
      "dataType": "int(11)",
      "isNullable": false,
      "keyInfo": "PRI",
      "defaultValue": null,
      "extraInfo": "auto_increment"
    },
    {
      "columnName": "username",
      "dataType": "varchar(50)",
      "isNullable": false,
      "keyInfo": "UNI",
      "defaultValue": null,
      "extraInfo": ""
    }
  ]
}
```

## MCP工具定义

为了与AI模型集成，此工具实现了以下MCP规范：

- **名称**: `mysql_schema_reader`
- **描述**: "一个可以连接到MySQL数据库并返回指定表结构的工具。可用于查询表的字段名、数据类型、键信息等。"
- **输入模式**: 需要 `tableName` 参数
- **输出模式**: 返回包含列详细信息的结构化模式信息

## 安全特性

- 无硬编码凭证 - 所有数据库凭证都从外部配置文件读取
- 输入验证以防止SQL注入攻击
- 使用正则表达式模式验证表名
- 使用反引号对表名进行适当的SQL转义

## 错误处理

该工具提供全面的错误处理，并为以下情况提供清晰的中文错误信息：
- 缺失或无效的配置文件
- 数据库连接失败
- 权限被拒绝错误
- 不存在的表或数据库
- 无效的命令或参数

## 系统要求

- Python 3.6+
- mysql-connector-python
- prettytable
- MySQL 5.7+ 或 MariaDB 10.2+

## 许可证

此工具作为MySQL Schema Reader MCP项目的一部分开发。
