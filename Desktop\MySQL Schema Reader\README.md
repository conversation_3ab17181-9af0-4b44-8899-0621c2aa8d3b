# MySQL Schema Reader MCP Tool

A command-line tool for reading MySQL database table structures. Supports both CLI output (ASCII tables) and JSON output for MCP integration.

## Features

- Query MySQL table structures with detailed column information
- List all tables in a database
- ASCII table output for human-readable results
- JSON output for MCP (Model Context Protocol) integration
- Secure configuration file-based credential management
- Comprehensive error handling with Chinese error messages
- SQL injection protection

## Installation

1. **Install Python dependencies:**
   ```bash
   pip install mysql-connector-python prettytable
   ```

2. **Create configuration file:**
   Copy `config.ini.example` to `config.ini` and update with your database credentials:
   ```ini
   [mysql]
   host = 127.0.0.1
   port = 3306
   user = your_username
   password = your_password
   database = your_database_name
   ```

3. **Set secure file permissions (recommended):**
   ```bash
   # On Linux/macOS
   chmod 600 config.ini
   ```

## Usage

### Basic Commands

**Describe a table structure:**
```bash
python mcp_mysql_reader.py describe --table users
```

**List all tables in database:**
```bash
python mcp_mysql_reader.py show-tables
```

### Advanced Options

**Use custom configuration file:**
```bash
python mcp_mysql_reader.py describe --table products --config /path/to/config.ini
```

**Override database from config:**
```bash
python mcp_mysql_reader.py describe --table orders --database shop_db
```

**Output in JSON format (for MCP integration):**
```bash
python mcp_mysql_reader.py describe --table users --json
```

**Show help:**
```bash
python mcp_mysql_reader.py --help
```

## Output Examples

### ASCII Table Output (CLI)
```
+-----------------+--------------+------+-----+---------+----------------+
| Field           | Type         | Null | Key | Default | Extra          |
+-----------------+--------------+------+-----+---------+----------------+
| id              | int(11)      | NO   | PRI | NULL    | auto_increment |
| username        | varchar(50)  | NO   | UNI | NULL    |                |
| email           | varchar(100) | NO   |     | NULL    |                |
| created_at      | timestamp    | YES  |     | NULL    |                |
+-----------------+--------------+------+-----+---------+----------------+
```

### JSON Output (MCP)
```json
{
  "schema": [
    {
      "columnName": "id",
      "dataType": "int(11)",
      "isNullable": false,
      "keyInfo": "PRI",
      "defaultValue": null,
      "extraInfo": "auto_increment"
    },
    {
      "columnName": "username",
      "dataType": "varchar(50)",
      "isNullable": false,
      "keyInfo": "UNI",
      "defaultValue": null,
      "extraInfo": ""
    }
  ]
}
```

## MCP Tool Definition

For AI model integration, this tool implements the following MCP specification:

- **name**: `mysql_schema_reader`
- **description**: "一个可以连接到MySQL数据库并返回指定表结构的工具。可用于查询表的字段名、数据类型、键信息等。"
- **inputSchema**: Requires `tableName` parameter
- **outputSchema**: Returns structured schema information with column details

## Security Features

- No hardcoded credentials - all database credentials are read from external config files
- Input validation to prevent SQL injection attacks
- Table name validation using regex patterns
- Proper SQL escaping with backticks for table names

## Error Handling

The tool provides comprehensive error handling with clear Chinese error messages for:
- Missing or invalid configuration files
- Database connection failures
- Permission denied errors
- Non-existent tables or databases
- Invalid commands or parameters

## Requirements

- Python 3.6+
- mysql-connector-python
- prettytable
- MySQL 5.7+ or MariaDB 10.2+

## License

This tool is developed as part of the MySQL Schema Reader MCP project.
