# MySQL Schema Reader MCP 工具

一个用于读取MySQL数据库表结构的命令行工具。支持CLI输出（ASCII表格）和用于MCP集成的JSON输出。

## 功能特性

- 查询MySQL表结构及详细列信息
- 列出数据库中的所有表
- ASCII表格输出，便于人类阅读
- JSON输出，用于MCP（模型上下文协议）集成
- 基于配置文件的安全凭证管理
- 全面的错误处理，提供中文错误信息
- SQL注入防护

## 安装说明

1. **安装Python依赖：**
   ```bash
   pip install mysql-connector-python prettytable
   ```

2. **创建配置文件：**
   将 `config.ini.example` 复制为 `config.ini` 并更新为您的数据库凭证：
   ```ini
   [mysql]
   host = 127.0.0.1
   port = 3306
   user = your_username
   password = your_password
   database = your_database_name
   ```

3. **设置安全文件权限（推荐）：**
   ```bash
   # 在 Linux/macOS 上
   chmod 600 config.ini
   ```

## 使用方法

### 基本命令

**描述表结构：**
```bash
python mcp_mysql_reader.py describe --table users
```

**列出数据库中的所有表：**
```bash
python mcp_mysql_reader.py show-tables
```

### 高级选项

**使用自定义配置文件：**
```bash
python mcp_mysql_reader.py describe --table products --config /path/to/config.ini
```

**覆盖配置中的数据库：**
```bash
python mcp_mysql_reader.py describe --table orders --database shop_db
```

**以JSON格式输出（用于MCP集成）：**
```bash
python mcp_mysql_reader.py describe --table users --json
```

**显示帮助信息：**
```bash
python mcp_mysql_reader.py --help
```

## 输出示例

### ASCII表格输出（CLI）
```
+-----------------+--------------+------+-----+---------+----------------+
| Field           | Type         | Null | Key | Default | Extra          |
+-----------------+--------------+------+-----+---------+----------------+
| id              | int(11)      | NO   | PRI | NULL    | auto_increment |
| username        | varchar(50)  | NO   | UNI | NULL    |                |
| email           | varchar(100) | NO   |     | NULL    |                |
| created_at      | timestamp    | YES  |     | NULL    |                |
+-----------------+--------------+------+-----+---------+----------------+
```

### JSON输出（MCP）
```json
{
  "schema": [
    {
      "columnName": "id",
      "dataType": "int(11)",
      "isNullable": false,
      "keyInfo": "PRI",
      "defaultValue": null,
      "extraInfo": "auto_increment"
    },
    {
      "columnName": "username",
      "dataType": "varchar(50)",
      "isNullable": false,
      "keyInfo": "UNI",
      "defaultValue": null,
      "extraInfo": ""
    }
  ]
}
```

## MCP工具定义

为了与AI模型集成，此工具实现了以下MCP规范：

- **名称**: `mysql_schema_reader`
- **描述**: "一个可以连接到MySQL数据库并返回指定表结构的工具。可用于查询表的字段名、数据类型、键信息等。"
- **输入模式**: 需要 `tableName` 参数
- **输出模式**: 返回包含列详细信息的结构化模式信息

## 作为MCP工具使用

### MCP服务器配置

要将MySQL Schema Reader作为MCP工具使用，需要在MCP服务器配置中注册此工具。以下是完整的配置步骤：

#### 1. 工具定义JSON配置

在MCP服务器的工具配置文件中添加以下工具定义：

```json
{
  "tools": [
    {
      "name": "mysql_schema_reader",
      "description": "一个可以连接到MySQL数据库并返回指定表结构的工具。可用于查询表的字段名、数据类型、键信息等。",
      "inputSchema": {
        "type": "object",
        "properties": {
          "tableName": {
            "type": "string",
            "description": "需要查询其结构的表的名称。"
          }
        },
        "required": ["tableName"]
      },
      "outputSchema": {
        "type": "object",
        "properties": {
          "schema": {
            "type": "array",
            "items": {
              "type": "object",
              "properties": {
                "columnName": {
                  "type": "string",
                  "description": "列（字段）的名称。"
                },
                "dataType": {
                  "type": "string",
                  "description": "列的数据类型。"
                },
                "isNullable": {
                  "type": "boolean",
                  "description": "该列是否允许存储NULL值。"
                },
                "keyInfo": {
                  "type": "string",
                  "description": "键信息 (例如 'PRI' 表示主键)。"
                },
                "defaultValue": {
                  "type": ["string", "null"],
                  "description": "该列的默认值。"
                },
                "extraInfo": {
                  "type": "string",
                  "description": "额外信息 (例如 'auto_increment')。"
                }
              },
              "required": ["columnName", "dataType", "isNullable", "keyInfo", "defaultValue", "extraInfo"]
            }
          }
        }
      },
      "executable": {
        "command": "python",
        "args": ["mcp_mysql_reader.py", "describe", "--table", "{tableName}", "--json"],
        "workingDirectory": "/path/to/mysql-schema-reader"
      }
    }
  ]
}
```

#### 2. 环境配置

确保MCP服务器环境中：
- 已安装Python 3.6+
- 已安装必需的依赖：`pip install mysql-connector-python prettytable`
- `config.ini` 文件已正确配置数据库连接信息
- `mcp_mysql_reader.py` 文件路径正确

### AI模型调用示例

#### 输入参数格式

AI模型调用此工具时，需要提供以下参数：

```json
{
  "tableName": "users"
}
```

**参数说明：**
- `tableName` (必需): 要查询的数据库表名称
- 表名必须是有效的MySQL表名（只包含字母、数字、下划线）
- 表名会自动进行安全验证以防止SQL注入

#### 输出格式说明

工具返回的JSON格式严格遵循以下结构：

```json
{
  "schema": [
    {
      "columnName": "字段名称",
      "dataType": "数据类型（如 varchar(50), int(11)）",
      "isNullable": true/false,
      "keyInfo": "键信息（PRI=主键, UNI=唯一键, MUL=索引, 空字符串=无键）",
      "defaultValue": "默认值或null",
      "extraInfo": "额外信息（如 auto_increment）"
    }
  ]
}
```

### 实际使用场景示例

以下是AI模型与用户交互时使用此工具的典型对话示例：

#### 场景1：查询用户表结构

**用户提问：**
> "请告诉我用户表(users)的结构是怎样的？"

**AI模型内部调用：**
```json
{
  "tool": "mysql_schema_reader",
  "parameters": {
    "tableName": "users"
  }
}
```

**工具返回结果：**
```json
{
  "schema": [
    {
      "columnName": "id",
      "dataType": "int(11)",
      "isNullable": false,
      "keyInfo": "PRI",
      "defaultValue": null,
      "extraInfo": "auto_increment"
    },
    {
      "columnName": "username",
      "dataType": "varchar(50)",
      "isNullable": false,
      "keyInfo": "UNI",
      "defaultValue": null,
      "extraInfo": ""
    },
    {
      "columnName": "email",
      "dataType": "varchar(100)",
      "isNullable": false,
      "keyInfo": "",
      "defaultValue": null,
      "extraInfo": ""
    },
    {
      "columnName": "created_at",
      "dataType": "timestamp",
      "isNullable": true,
      "keyInfo": "",
      "defaultValue": "CURRENT_TIMESTAMP",
      "extraInfo": ""
    }
  ]
}
```

**AI模型回复用户：**
> "用户表(users)包含以下字段：
>
> 1. **id** (int(11)) - 主键，自动递增，不允许为空
> 2. **username** (varchar(50)) - 用户名，唯一键，不允许为空
> 3. **email** (varchar(100)) - 邮箱地址，不允许为空
> 4. **created_at** (timestamp) - 创建时间，允许为空，默认值为当前时间戳
>
> 这个表的主键是id字段，username字段有唯一性约束。"

#### 场景2：数据库设计咨询

**用户提问：**
> "我想了解订单表的字段设计，帮我分析一下orders表的结构合理性"

**AI模型调用工具并分析：**
基于工具返回的表结构信息，AI可以提供专业的数据库设计建议，包括：
- 字段类型是否合适
- 索引设计是否合理
- 是否缺少必要的约束
- 性能优化建议

### 错误处理

当工具执行出现错误时，会返回相应的中文错误信息：

```json
{
  "error": "错误：在数据库 'shop_db' 中未找到表 'nonexistent_table'。"
}
```

常见错误类型：
- 表不存在
- 数据库连接失败
- 权限不足
- 配置文件错误

### 最佳实践

1. **安全配置**：确保数据库用户只有必要的读取权限
2. **性能考虑**：对于大型数据库，建议限制可查询的表范围
3. **错误监控**：记录工具调用日志以便问题排查
4. **定期维护**：定期检查数据库连接配置的有效性

## 安全特性

- 无硬编码凭证 - 所有数据库凭证都从外部配置文件读取
- 输入验证以防止SQL注入攻击
- 使用正则表达式模式验证表名
- 使用反引号对表名进行适当的SQL转义

## 错误处理

该工具提供全面的错误处理，并为以下情况提供清晰的中文错误信息：
- 缺失或无效的配置文件
- 数据库连接失败
- 权限被拒绝错误
- 不存在的表或数据库
- 无效的命令或参数

## 系统要求

- Python 3.6+
- mysql-connector-python
- prettytable
- MySQL 5.7+ 或 MariaDB 10.2+

## 许可证

此工具作为MySQL Schema Reader MCP项目的一部分开发。
