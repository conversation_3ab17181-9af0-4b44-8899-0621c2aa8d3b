#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL Schema Reader MCP Tool

A command-line tool for reading MySQL database table structures.
Supports both CLI output (ASCII tables) and JSON output for MCP integration.

Author: Augment Agent
Version: 1.0
Date: 2025-07-15
"""

import argparse
import configparser
import json
import os
import sys
from typing import Dict, List, Optional, Tuple, Any

try:
    import mysql.connector
    from mysql.connector import Error as MySQLError
except ImportError:
    print("错误：缺少必需的依赖 'mysql-connector-python'。请运行: pip install mysql-connector-python")
    sys.exit(1)

try:
    from prettytable import PrettyTable
except ImportError:
    print("错误：缺少必需的依赖 'prettytable'。请运行: pip install prettytable")
    sys.exit(1)


class MySQLSchemaReader:
    """MySQL Schema Reader main class"""
    
    def __init__(self, config_path: str = "config.ini"):
        """
        Initialize the MySQL Schema Reader
        
        Args:
            config_path: Path to the configuration file
        """
        self.config_path = config_path
        self.config = None
        self.connection = None
    
    def load_config(self) -> Dict[str, str]:
        """
        Load and parse the configuration file
        
        Returns:
            Dictionary containing database configuration
            
        Raises:
            SystemExit: If config file is missing or invalid
        """
        if not os.path.exists(self.config_path):
            print(f"错误：配置文件 '{self.config_path}' 未找到。请在当前目录创建该文件或使用 --config 参数指定路径。")
            sys.exit(1)
        
        try:
            config = configparser.ConfigParser()
            config.read(self.config_path, encoding='utf-8')
            
            if 'mysql' not in config:
                print(f"错误：配置文件 '{self.config_path}' 解析失败。请检查文件格式是否正确。")
                sys.exit(1)
            
            mysql_config = dict(config['mysql'])
            
            # Validate required fields
            required_fields = ['host', 'port', 'user', 'password', 'database']
            for field in required_fields:
                if field not in mysql_config:
                    print(f"错误：配置文件 '{self.config_path}' 解析失败。请检查文件格式是否正确。")
                    sys.exit(1)
            
            # Convert port to integer
            try:
                mysql_config['port'] = int(mysql_config['port'])
            except ValueError:
                print(f"错误：配置文件 '{self.config_path}' 解析失败。请检查文件格式是否正确。")
                sys.exit(1)
            
            self.config = mysql_config
            return mysql_config
            
        except Exception as e:
            print(f"错误：配置文件 '{self.config_path}' 解析失败。请检查文件格式是否正确。")
            sys.exit(1)
    
    def validate_table_name(self, table_name: str) -> bool:
        """
        Validate table name to prevent SQL injection
        
        Args:
            table_name: The table name to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not table_name:
            return False
        
        # Check for dangerous characters
        dangerous_chars = [';', '--', '/*', '*/', "'", '"']
        for char in dangerous_chars:
            if char in table_name:
                return False
        
        # Check if it contains only valid characters (letters, numbers, underscores)
        import re
        if not re.match(r'^[a-zA-Z0-9_]+$', table_name):
            return False
        
        return True
    
    def connect_to_database(self, database_override: Optional[str] = None) -> None:
        """
        Establish connection to MySQL database
        
        Args:
            database_override: Optional database name to override config default
            
        Raises:
            SystemExit: If connection fails
        """
        if not self.config:
            self.load_config()
        
        try:
            connection_config = self.config.copy()
            if database_override:
                connection_config['database'] = database_override
            
            self.connection = mysql.connector.connect(**connection_config)
            
        except MySQLError as e:
            if e.errno == 1045:  # Access denied
                user = self.config.get('user', 'unknown')
                database = database_override or self.config.get('database', 'unknown')
                print(f"错误：用户 '{user}' 没有足够的权限访问数据库 '{database}' 或表。")
            elif e.errno == 2003:  # Can't connect to server
                print("错误：无法连接到数据库。请检查配置文件中的主机、端口、用户名和密码是否正确，并确保数据库服务正在运行。")
            elif e.errno == 1049:  # Unknown database
                database = database_override or self.config.get('database', 'unknown')
                print(f"错误：数据库 '{database}' 不存在。")
            else:
                print("错误：无法连接到数据库。请检查配置文件中的主机、端口、用户名和密码是否正确，并确保数据库服务正在运行。")
            sys.exit(1)
        except Exception as e:
            print("错误：无法连接到数据库。请检查配置文件中的主机、端口、用户名和密码是否正确，并确保数据库服务正在运行。")
            sys.exit(1)
    
    def close_connection(self) -> None:
        """Close the database connection"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
    
    def describe_table(self, table_name: str, output_json: bool = False) -> None:
        """
        Describe the structure of a table
        
        Args:
            table_name: Name of the table to describe
            output_json: Whether to output in JSON format
        """
        if not self.validate_table_name(table_name):
            print(f"错误：表名 '{table_name}' 包含无效字符。")
            sys.exit(1)
        
        try:
            cursor = self.connection.cursor()
            
            # Use backticks to escape table name
            query = f"DESCRIBE `{table_name}`"
            cursor.execute(query)
            
            results = cursor.fetchall()
            
            if not results:
                database = self.config.get('database', 'unknown')
                print(f"错误：在数据库 '{database}' 中未找到表 '{table_name}'。")
                sys.exit(1)
            
            if output_json:
                self._output_describe_json(results)
            else:
                self._output_describe_table(results)
                
        except MySQLError as e:
            if e.errno == 1146:  # Table doesn't exist
                database = self.config.get('database', 'unknown')
                print(f"错误：在数据库 '{database}' 中未找到表 '{table_name}'。")
            elif e.errno == 1142:  # SELECT command denied
                user = self.config.get('user', 'unknown')
                database = self.config.get('database', 'unknown')
                print(f"错误：用户 '{user}' 没有足够的权限访问数据库 '{database}' 或表 '{table_name}'。")
            else:
                print(f"错误：查询表结构时发生错误：{e}")
            sys.exit(1)
        finally:
            if cursor:
                cursor.close()
    
    def show_tables(self) -> None:
        """Show all tables in the current database"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("SHOW TABLES")
            
            results = cursor.fetchall()
            
            if not results:
                database = self.config.get('database', 'unknown')
                print(f"数据库 '{database}' 中没有表。")
                return
            
            # Create table for output
            table = PrettyTable()
            database_name = self.config.get('database', 'unknown')
            table.field_names = [f"Tables_in_{database_name}"]
            
            for row in results:
                table.add_row([row[0]])
            
            print(table)
            
        except MySQLError as e:
            if e.errno == 1142:  # SELECT command denied
                user = self.config.get('user', 'unknown')
                database = self.config.get('database', 'unknown')
                print(f"错误：用户 '{user}' 没有足够的权限访问数据库 '{database}'。")
            else:
                print(f"错误：查询表列表时发生错误：{e}")
            sys.exit(1)
        finally:
            if cursor:
                cursor.close()
    
    def _output_describe_table(self, results: List[Tuple]) -> None:
        """
        Output table description in ASCII table format
        
        Args:
            results: Query results from DESCRIBE command
        """
        table = PrettyTable()
        table.field_names = ["Field", "Type", "Null", "Key", "Default", "Extra"]
        
        for row in results:
            # Convert None to "NULL" for display
            formatted_row = []
            for item in row:
                if item is None:
                    formatted_row.append("NULL")
                else:
                    formatted_row.append(str(item))
            table.add_row(formatted_row)
        
        print(table)
    
    def _output_describe_json(self, results: List[Tuple]) -> None:
        """
        Output table description in JSON format for MCP
        
        Args:
            results: Query results from DESCRIBE command
        """
        schema = []
        
        for row in results:
            field, data_type, null, key, default, extra = row
            
            # Convert database NULL representation to proper types
            is_nullable = null.upper() == 'YES' if null else False
            key_info = key if key else ""
            default_value = default if default is not None else None
            extra_info = extra if extra else ""
            
            column_info = {
                "columnName": field,
                "dataType": data_type,
                "isNullable": is_nullable,
                "keyInfo": key_info,
                "defaultValue": default_value,
                "extraInfo": extra_info
            }
            
            schema.append(column_info)
        
        output = {"schema": schema}
        print(json.dumps(output, indent=2, ensure_ascii=False))


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="MySQL Schema Reader MCP Tool - 查询MySQL数据库表结构的命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s describe --table users
  %(prog)s describe --table products --database shop
  %(prog)s describe --table orders --json
  %(prog)s show-tables
  %(prog)s show-tables --config /path/to/config.ini
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # Describe command
    describe_parser = subparsers.add_parser('describe', help='显示指定表的结构')
    describe_parser.add_argument('--table', required=True, help='要查询的表名')
    describe_parser.add_argument('--json', action='store_true', help='以JSON格式输出（用于MCP集成）')
    
    # Show tables command
    subparsers.add_parser('show-tables', help='显示当前数据库中的所有表')
    
    # Global arguments
    parser.add_argument('--database', help='指定要连接的数据库（覆盖配置文件中的默认值）')
    parser.add_argument('--config', default='config.ini', help='配置文件路径（默认: config.ini）')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # Validate command
    if args.command not in ['describe', 'show-tables']:
        print(f"错误：无效的命令 '{args.command}'。请使用 '{parser.prog} --help' 查看可用命令。")
        sys.exit(1)
    
    # Initialize schema reader
    reader = MySQLSchemaReader(args.config)
    
    try:
        # Load configuration and connect to database
        reader.load_config()
        reader.connect_to_database(args.database)
        
        # Execute command
        if args.command == 'describe':
            reader.describe_table(args.table, args.json)
        elif args.command == 'show-tables':
            reader.show_tables()
            
    finally:
        reader.close_connection()


if __name__ == "__main__":
    main()
