# MySQL Schema Reader MCP 工具 - 快速启动指南

> 🚀 **目标**：10分钟内完成配置并成功运行工具

## 前置要求

### 系统环境
- ✅ **Python 3.6+** 已安装
- ✅ **MySQL数据库** 可访问
- ✅ **网络连接** 正常

### 快速检查
```bash
# 检查Python版本
python --version

# 检查MySQL连接（可选）
mysql -h your_host -u your_user -p
```

## 第一步：安装依赖

```bash
# 安装必需的Python包
pip install mysql-connector-python prettytable
```

## 第二步：配置数据库连接

### 创建配置文件
复制 `config.ini.example` 为 `config.ini`：

```bash
cp config.ini.example config.ini
```

### 编辑配置文件
打开 `config.ini` 并填入您的数据库信息：

```ini
[mysql]
host = 127.0.0.1
port = 3306
user = your_username
password = your_password
database = your_database_name
```

### 设置文件权限（推荐）
```bash
# Linux/macOS
chmod 600 config.ini

# Windows（可选）
icacls config.ini /grant:r "%USERNAME%":F /inheritance:r
```

## 第三步：验证安装

### 测试基本功能
```bash
# 显示帮助信息
python mcp_mysql_reader.py --help

# 列出数据库中的所有表
python mcp_mysql_reader.py show-tables

# 查看指定表结构
python mcp_mysql_reader.py describe --table your_table_name
```

## 三种使用方式

### 方式1：CLI命令行模式

**适用场景**：直接在终端中查询表结构

```bash
# 查看表结构（ASCII表格格式）
python mcp_mysql_reader.py describe --table users

# 查看表结构（JSON格式）
python mcp_mysql_reader.py describe --table users --json

# 指定数据库
python mcp_mysql_reader.py describe --table products --database shop_db

# 使用自定义配置文件
python mcp_mysql_reader.py describe --table orders --config /path/to/config.ini
```

### 方式2：MCP服务器模式

**适用场景**：作为MCP服务器供其他应用调用

#### 启动MCP服务器
```bash
# 方法1：显式启动
python mcp_mysql_reader.py --mcp-server

# 方法2：无参数启动（自动检测）
python mcp_mysql_reader.py
```

#### MCP客户端配置
在MCP客户端配置文件中添加：

```json
{
  "mcpServers": {
    "mysql-schema-reader": {
      "command": "python",
      "args": ["mcp_mysql_reader.py"],
      "cwd": "/path/to/mysql-schema-reader",
      "env": {
        "PYTHONPATH": "/path/to/mysql-schema-reader"
      }
    }
  }
}
```

### 方式3：与AI模型集成

**适用场景**：AI模型自动查询数据库表结构

#### 工具调用示例
AI模型会发送如下请求：
```json
{
  "method": "tools/call",
  "params": {
    "name": "mysql_schema_reader",
    "arguments": {
      "tableName": "users"
    }
  }
}
```

#### 典型对话场景
```
用户: "请告诉我用户表的结构"
AI: 调用mysql_schema_reader工具查询users表
AI: "用户表包含以下字段：id(主键)、username(唯一)、email、created_at..."
```

## 常见问题快速解决

### ❌ 问题1：`command not found: python`
**解决方案：**
```bash
# 尝试使用python3
python3 --version
# 或使用完整路径
/usr/bin/python3 mcp_mysql_reader.py --help
```

### ❌ 问题2：`ModuleNotFoundError: No module named 'mysql.connector'`
**解决方案：**
```bash
# 重新安装依赖
pip install --upgrade mysql-connector-python prettytable
# 或使用pip3
pip3 install mysql-connector-python prettytable
```

### ❌ 问题3：`错误：配置文件 'config.ini' 未找到`
**解决方案：**
```bash
# 检查文件是否存在
ls -la config.ini
# 检查当前目录
pwd
# 使用绝对路径
python mcp_mysql_reader.py describe --table users --config /full/path/to/config.ini
```

### ❌ 问题4：`错误：无法连接到数据库`
**解决方案：**
```bash
# 测试数据库连接
mysql -h your_host -u your_user -p your_database
# 检查配置文件格式
cat config.ini
# 确认数据库服务运行
systemctl status mysql  # Linux
brew services list | grep mysql  # macOS
```

### ❌ 问题5：`错误：在数据库中未找到表`
**解决方案：**
```bash
# 先查看所有表
python mcp_mysql_reader.py show-tables
# 确认表名拼写正确
python mcp_mysql_reader.py describe --table correct_table_name
```

## 快速测试清单

完成配置后，请依次执行以下命令验证：

```bash
# ✅ 1. 基本功能测试
python mcp_mysql_reader.py --help

# ✅ 2. 数据库连接测试
python mcp_mysql_reader.py show-tables

# ✅ 3. 表结构查询测试
python mcp_mysql_reader.py describe --table your_table_name

# ✅ 4. JSON输出测试
python mcp_mysql_reader.py describe --table your_table_name --json

# ✅ 5. MCP服务器模式测试
python mcp_mysql_reader.py --mcp-server
# 按Ctrl+C退出
```

## 下一步

配置成功后，您可以：

1. **阅读完整文档**：查看 `README.md` 了解更多功能
2. **配置MCP集成**：参考 `MCP配置说明.md` 进行高级配置
3. **安全加固**：设置数据库用户权限和文件访问控制
4. **性能优化**：根据使用情况调整数据库连接参数

## 获取帮助

- 📖 **完整文档**：`README.md`
- 🔧 **配置指南**：`MCP配置说明.md`
- 🚀 **部署示例**：`标准MCP配置完整示例.md`
- ❓ **问题排查**：检查错误信息并参考上述常见问题解决方案

---

**🎉 恭喜！您已成功配置MySQL Schema Reader MCP工具！**
